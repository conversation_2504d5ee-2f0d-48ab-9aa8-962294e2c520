{"name": "bysweb", "version": "2.2.18", "description": "Beifeng BF-PMSS01 smart electronic boundary stake system", "productName": "BF智慧界桩", "author": "linfl <<EMAIL>>", "private": true, "scripts": {"serve": "quasar dev", "serve:android": "./gen-build-properties.sh && quasar dev -m capacitor -T android", "build": "quasar build", "android:debug": "./gen-build-properties.sh && quasar build -m capacitor -T android --debug", "lint": "eslint --fix --ext .js,.ts,.vue --ignore-path .gitignore --ignore-path .eslintignore ./src", "build:android": "./build-android.sh", "proto": "./build-proto.sh", "submodule:pull": "git submodule foreach git pull", "submodule:commit": "git commit -o git.kicad99.com -m \"chore(Git submodule): sync submodule\"", "submodule:sync": "yarn submodule:pull && yarn submodule:commit", "submodule:master": "git submodule foreach git switch master", "submodule:init": "git submodule update --init --recursive", "test": "echo test", "icongenie": "./icongenie.sh"}, "dependencies": {"@mapbox/mapbox-gl-draw": "^1.4.3", "@quasar/extras": "^1.16.9", "core-js": "^3.36.1", "crypto-js": "^4.0.0", "dayjs": "^1.8.36", "gcoord": "^1.0.6", "jquery": "^3.5.1", "jquery-ui": "^1.12.1", "jquery.fancytree": "^2.37.0", "jsyrpc": "^1.4.1", "lodash": "^4.17.19", "loglevel": "^1.7.0", "maplibre-gl": "^4.1.2", "node-forge": "^1.3.1", "pako": "^2.0.3", "protobufjs": "^7.2.6", "protobufjs-patch": "^1.0.5", "quasar": "^2.15.1", "semver": "^7.7.2", "turf": "^3.0.14", "ui-contextmenu": "^1.18.1", "uuid": "^9.0.1", "vue": "^3.4.21", "vue-easy-lightbox": "^1.19.0", "vue-i18n": "^9.10.2", "vue-router": "^4.3.0", "vuex": "^4.1.0", "xlsx": "^0.18.5", "ypubsub": "^1.0.12", "yrpcmsg": "^1.2.5"}, "devDependencies": {"@quasar/app-vite": "^2.0.0-beta.5", "@quasar/icongenie": "^4.0.0", "@types/crypto-js": "^4.2.2", "@types/jquery": "^3.3.38", "@types/jquery.fancytree": "^0.0.10", "@types/lodash": "^4.17.0", "@types/long": "^5.0.0", "@types/node": "^20.11.30", "@types/pako": "^2.0.3", "@types/turf": "^3.5.32", "@types/uuid": "^9.0.8", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "autoprefixer": "^10.4.19", "dotenv": "^16.4.7", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^9.24.0", "postcss": "^8.4.38", "prettier": "^3.2.5", "tailwindcss": "^3.4.3", "typescript": "^5.4.3"}, "engines": {"node": ">= 16.0.0"}, "browserslist": ["last 4 version, not dead, ie >= 11"], "appName": "BFPMSS01", "companyName": "Beifeng", "appId": "beifeng.scenic.system"}